import { BoundingRect } from './agent-state';
import { FormVisionResult } from '../../form-generation/htmx-generator';
import { ClassificationResult } from '../../form-generation/types/classification-interfaces';

export type Action = {
  type: 'click' | 'fill' | 'acknowledge' | 'select';
  name: string;
  value?: string;
  coordinates: {
    x: number;
    y: number;
  };
  isSocialLogin?: boolean;
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

// Action without coordinates for Phase 1 fast response
export type ActionWithoutCoordinates = {
  type: 'click' | 'fill' | 'acknowledge' | 'select';
  name: string;
  value?: string;
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

// Action with optional coordinates for intermediate processing
export type ActionWithOptionalCoordinates = {
  type: 'click' | 'fill' | 'acknowledge' | 'select';
  name: string;
  value?: string;
  coordinates?: {
    x: number;
    y: number;
  };
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

export type PageStateResult = {
  extractionResult: FormVisionResult;
  classificationResult: ClassificationResult;
};

export type PageStateResultWithOptionalCoordinates = {
  extractionResult: FormVisionResult;
  classificationResult: ClassificationResult;
  coordinatesResolved?: boolean; // Flag to track if coordinates are available
  coordinateResolutionPromise?: Promise<void>; // Promise for coordinate resolution
};

export type CaptchaBoundingBox = Omit<BoundingRect, 'id'>;
