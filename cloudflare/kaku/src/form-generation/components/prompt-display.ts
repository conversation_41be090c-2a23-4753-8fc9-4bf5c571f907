/**
 * Prompt display component
 */

/**
 * Generate prompt display markup for verification codes and contextual information
 */
export function generatePromptDisplay(
  instruction: string | null,
  screenCode: number | null,
  displayInstruction: boolean = true,
): string {
  if (!instruction && !screenCode) return '';

  const elements: string[] = [];

  if (displayInstruction && instruction) {
    elements.push(`
      <div class="mb-2">${instruction}</div>
    `);
  }

  if (screenCode) {
    elements.push(`
        <div class="verification-code-value">${screenCode}</div>
    `);
  }
  return `<div class="prompt-container">${elements.join('')}</div>`;
}
