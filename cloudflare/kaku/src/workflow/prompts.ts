// Centralized LLM prompt constants for use in Workers and tests

export const FORM_VISION_PROMPT_V6 = `
Task: From the image, extract a JSON object of the UI controls essential for an existing user's authentication flow.

**Extraction Rules**

1.  **Explicit Exclusions Rules:**
    *   Explicit exclusions supersede all other rules.
    *   Strictly ignore and exclude all controls related to **Account Creation**, **Account Recovery**, **Password Visibility**, **Get Help** and **Profile Management**.
    *   Strictly ignore and exclude Links or controls that explicitly suggest downloading, installing, or setting up a new application, service, or feature to perform a verification task or to gain access to a different method of authentication. 

2.  **Included Controls Rules:**
    *   Extract social media login buttons (e.g., "Continue with Google", "Login with Facebook", "Continue with Apple", OAuth provider buttons).
    *   Extract controls for standard credential entry (e.g., username/email fields).
    *   Extract controls for Multi-Factor Authentication (MFA) or identity verification (e.g., radio buttons for choosing a method, fields for entering a code).
    *   Extract controls that allow the user to remain authenticated or have their device remembered (e.g., "Remember me," "Stay signed in," "Trust this device").
    *   Extract the primary submission button required to proceed (e.g., "Sign In," "Continue," "Verify").

3.  **Radio Group Handling:**
    *   When you identify a group of mutually exclusive radio buttons, you **must** consolidate them into a **single** \`field\` object with \`fieldControlType: 'radiogroup'\`.
    *   The \`label\` for this single field object must be the main question or heading for the entire group (e.g., "Choose a way to confirm it's you").
    *   The \`options\` array of this single field object must contain an entry for each individual radio button. Each entry should include its own \`id\`, \`label\`, \`description\` (if any sub-text exists), and \`checked\` status.
    *   **Do not** create a separate \`field\` object for each individual radio button.

4.  **Grounding:**
    *   Only extract controls that are visibly present in the image. Do not infer or hallucinate. Honor the explicit exclusions above.
    
Version: 7
`;

export const FORM_VISION_CLASSIFICATION_PROMPT_V34 = `
Task: Your function is to analyze the image to determine its primary classification and the user's authentication status. Apply the rules below in order, selecting the first one that matches. Only output a raw JSON object without any fencing or additional text.

**Prioritized Classification & AuthState Rules**

1.  **"loading-screen":**
    * **Classification:** The primary visible content indicates a process is in progress, including (but not limited to):
        - Spinners, rotating circles, pulsing dots, animated loaders.
        - Progress bars (determinate or indeterminate).
        - “Loading…”, “Please wait”, “Fetching data” text.
        - Blurred, greyed-out, or skeleton placeholders (UI shapes without content).
        - Splash/loading pages with minimal branding and no interactive fields.
    *   **\`authState\` Rule:** Set to \`not-authenticated\`. A loading screen during login is the most common case.

2.  **"captcha-screen":**
    *   **Classification:** The screen contains a challenge to distinguish a human from a bot (e.g., reCAPTCHA, hCaptcha, image puzzles).
    *   **\`authState\` Rule:** Set to \`not-authenticated\`. CAPTCHAs are most frequently part of a login or signup flow.

3.  **"passkey-screen":**
    *   **Classification:** The screen explicitly prompts for a passkey, security key, WebAuthn, FIDO, or a biometric identifier.
    *   **\`authState\` Rule:
          ** If the screen's indicates the user is being prompted to **create** a passkey. Set to \`authenticated\`. Set \`authStateReasoning\` to "User is authenticated"
          ** If the screen's indicates the user is being prompted to **use** a passkey, Set to \`not-authenticated\`. Set \`authStateReasoning\` to "User is not authenticated."
    *   **\`title\` Rule:** Override value and set to \`Passkey Authentication\`.
    *   **\`description\` Rule:** Override value and set to \`Sorry, Passkeys are not supported\`.

4.  **"multi-factor-verification-screen":**
    *   **multi-factor-push-approval-screen:**
          **Classification:** The screen is part of a multi-factor authentication (MFA) flow, asking the user to enter a verification code or approve the login from another device. At this stage of the login flow, the user is not yet authenticated.
          **\`authState\` Rule:** Set to \`not-authenticated\`.
          **Extraction Rule:** Look for a visible one-time code presented as static text on the screen (e.g., "Your code is 123-456"). If a code is found, extract the numeric or alphanumeric string and place it in the \`verificationCode\` field. If the MFA method does not display a code (e.g., it's a push notification) or no code is visible, the \`verificationCode\` field should be set to null.

    *   **multi-factor-code-verification-screen:**
          **Classification:** The screen is part of a multi-factor authentication (MFA) flow, asking the user to enter a verification code. At this stage of the login flow, the user is not yet authenticated.
          **\`authState\` Rule:** Set to \`not-authenticated\`.

    *   **multi-factor-multiple-options-screen:**
          **Classification:** The screen is part of a multi-factor authentication (MFA) flow, asking the user to select from multiple verification options (e.g., "Send code via SMS", "Use authenticator app", "Email me a code"). At this stage of the login flow, the user is not yet authenticated AND the user has not selected a verification option.
          **\`authState\` Rule:** Set to \`not-authenticated\`.

5.  **"trust-device-screen":**
    *   **Classification:** The screen asks the user if they want to "trust" or "remember" the current device to simplify future logins. Look for phrases like "Trust this browser?", "Remember me", or "Stay signed in?".
    *   **\`authState\` Rule:** The user has proven their identity but the login flow is not complete. Set to \`authenticated\`.

6.  **"profile-management-screen":**
    *   **Classification:** The screen allows a user to view or change their account settings (e.g., updating personal info, changing a password).
    *   **\`authState\` Rule:** A user must be logged in to manage their profile. This is the primary indicator of an active session. Set to \`authenticated\`.

7. **"logged-in-screen":**
    *   **Classification:** The screen shows a post-authentication experience (e.g., dashboard, home/feed, inbox, account/usage overview, order history) and includes at least one signed-in indicator such as “Welcome back, [name]”, a visible user avatar/profile menu, a “Sign out/Log out” action, or a user identifier (email/username) shown as part of the UI—not inside a login form. It is not an MFA, passkey, trust-device, loading, captcha, or dedicated profile/settings editor screen.
    *   **\`authState\` Rule:** Set to \`authenticated\`.
    *   Notes: If any earlier rule (1–6) matches, use that earlier rule instead of this one.

8.  **"other":**
    *   **Classification:** The screen does not match any of the above categories. This includes standard username/password login forms, account creation pages, and generic error screens.
    *   **\`authState\` Rule:** These screens all precede a successful login. Set to \`not-authenticated\`.

**Grounding:**
*   Base your classification and \`authState\` decision solely on the visible content of the image and the rules above.

Version: 15
`;
