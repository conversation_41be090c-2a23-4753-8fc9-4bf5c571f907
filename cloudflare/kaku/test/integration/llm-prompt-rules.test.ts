import { env } from 'cloudflare:test';
import { beforeAll, describe, expect, it } from 'vitest';
// import { FormVisionResult } from '../../src/form-generation/types/form-interfaces';
import { PageStateResult } from '../../src/agent/types/extract-result';
import { FormVisionResult } from '../../src/form-generation/htmx-generator';
import { GeminiLLMRepository } from '../../src/llm/GeminiLLMRepository';
import { makeParallelLLMCalls } from '../../src/llm/llm-parallel-calls';
import { LLMService } from '../../src/llm/LLMService';
import { OpenAILLMRepository } from '../../src/llm/OpenAILLMRepository';
import { DetectPageStateChangeResponse } from '../../src/llm/types/detect-page-state-change-response';
import { PlatformTypes } from '../../src/ui/constants';
import {
  FORM_VISION_CLASSIFICATION_PROMPT_V34,
  FORM_VISION_PROMPT_V6,
} from '../../src/workflow/prompts';
import {
  getPlatformVersion,
  pageStateResultComparisonPromptInstructions,
} from '../../src/workflow/utils/constants';
import { parseStateDetectionResult } from '../../src/workflow/utils/helpers';
import { facebookLogin, googleLogin } from '../common/form-vision-result-examples';
import { StateComparisonTester } from '../common/state-comparison-tester';
import { GroqLLMRepository } from '../../src/llm/GroqLLMRepository';

const GEMINI_API_KEY = ''; //TODO(Add API key here to run this script)
const GEMINI_BASE_URL =
  env.AI_GATEWAY_GEMINI_URL ||
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio';

const OPENAI_API_KEY = ''; //TODO(Add API key here to run this script)
const OPENAI_BASE_URL =
  env.AI_GATEWAY_OPENAI_URL ||
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/openai';

const GROK_API_KEY = ''; //TODO(Add API key here to run this script)
const GROK_BASE_URL =
  env.AI_GATEWAY_GROK_URL ||
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/groq';

const DEFAULT_TEST_ITERATIONS = 10; // Number of iterations for each test

const TEST_TIMEOUT = 60000; // 60s - 6s for each of the 10 iterations

const imageBaseURL = env.KAKU_API_ENDPOINT;

async function fetchScreenshot(fileName: string): Promise<string> {
  try {
    const url = `${imageBaseURL}/${fileName}`;
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch ${fileName}: ${response.statusText}`);
    }
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error(`Failed to fetch screenshot ${fileName}:`, error);
    throw error;
  }
}

describe.skip('LLM Prompt Rules Test', () => {
  let llmService: LLMService;
  let geminiRepository: GeminiLLMRepository;
  let grokRepository: GroqLLMRepository;

  beforeAll(() => {
    if (!GEMINI_API_KEY) {
      console.error('Gemini API key is missing.');
      throw new Error('Gemini API key is missing.');
    }
    if (!OPENAI_API_KEY) {
      console.error('OpenAI API key is missing.');
      throw new Error('OpenAI API key is missing.');
    }
    /*if (!GROK_API_KEY) {
      console.error('Grok API key is missing.');
      throw new Error('Grok API key is missing.');
    }*/

    geminiRepository = new GeminiLLMRepository(GEMINI_API_KEY, GEMINI_BASE_URL);
    grokRepository = new GroqLLMRepository(GROK_API_KEY, GROK_BASE_URL);

    llmService = new LLMService({
      //primaryRepo: grokRepository,
      primaryRepo: geminiRepository,
      secondaryRepo: new OpenAILLMRepository(OPENAI_API_KEY, OPENAI_BASE_URL),
    });

    console.log('LLM Service initialized');
  });

  const runTest = async (
    testName: string,
    screenshotPath: string,
    platform: PlatformTypes,
    validate: (result: PageStateResult) => void,
  ): Promise<void> => {
    const screenshot = await fetchScreenshot(screenshotPath);

    const version = getPlatformVersion(platform, env);

    for (let i = 1; i <= DEFAULT_TEST_ITERATIONS; i++) {
      const result = await makeParallelLLMCalls(
        llmService,
        {
          platform: platform,
          screenshot,
          skipCache: true,
          viewportWidth: 800,
          viewportHeight: 600,
          version,
        },
        FORM_VISION_PROMPT_V6,
        FORM_VISION_CLASSIFICATION_PROMPT_V34,
      );

      validate(result);
    }
  };

  const runStateComparisonTest = async (
    formVisualResult: FormVisionResult,
    expectedHasChanges: boolean,
    screenshotPath: string,
    platform: PlatformTypes,
  ): Promise<void> => {
    const screenshot = await fetchScreenshot(screenshotPath);
    const geminiResponse = await geminiRepository.detectStateChangeFromPreviousFormVisionResult({
      platform: platform,
      prompt: pageStateResultComparisonPromptInstructions,
      agentVisionResultState: formVisualResult,
      screenshot: screenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    });

    let detectPageStateChangeResponse: DetectPageStateChangeResponse;

    try {
      detectPageStateChangeResponse = parseStateDetectionResult(geminiResponse.output_text);
    } catch (error) {
      throw new Error(`Invalid JSON response from form vision analysis: ${error}`);
    }

    expect(detectPageStateChangeResponse.hasChanges).toBe(expectedHasChanges);
  };

  // Error Message Detection: Detect error messages
  it(
    'should detect error messages',
    async () => {
      await runTest(
        'Error messages detection',
        'test/files/screenshots/google_wrong_password.png',
        'google',
        (result) => {
          // from Extraction result
          expect(result.extractionResult.screenInfo.errors?.length).toBeGreaterThan(0);
          expect(result.extractionResult.screenInfo.errors?.[0]).toBeDefined();

          // from Classification result
          expect(result.classificationResult.screenInfo.alerts?.length).toBeGreaterThan(0);
          expect(result.classificationResult.screenInfo.alerts?.[0]).toBeDefined();
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Error Message Detection: Detect multiline error messages
  it(
    'should handle multiline error messages',
    async () => {
      await runTest(
        'Multiline error messages detection',
        'test/files/screenshots/facebook-multiline-errors.png',
        'facebook',
        (result) => {
          // from Extraction result
          expect(result.extractionResult.screenInfo.errors).toBeDefined();
          expect(result.extractionResult.screenInfo.errors?.length).toBe(3);

          // from Classification result
          expect(result.classificationResult.screenInfo.alerts).toBeDefined();
          expect(result.classificationResult.screenInfo.alerts?.length).toBe(1);
          expect(result.classificationResult.screenInfo.alertsCount).toBeGreaterThanOrEqual(1);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Exclusion Rule: Exclude "Forgot Password" or "Reset Password"
  it(
    "should exclude 'Forgot Password' or 'Reset Password'",
    async () => {
      await runTest(
        'Exclude Forgot Password or Reset Password',
        'test/files/screenshots/facebook_login.webp',
        'facebook',
        (result) => {
          const forgotPasswordDetectionRegex =
            /\b(?:forgot(?:ten)?|reset)(?:\s+your)?\s+password\b\??/i;

          const excludedControls =
            result.extractionResult.screenInfo.controlVisibilityRules?.filter((rule) =>
              rule.id.replace(/_/g, ' ').toLowerCase().match(forgotPasswordDetectionRegex),
            );
          excludedControls?.forEach((rule) => {
            expect(rule.status).toBe('excluded');
          });

          const allControls = [
            ...result.extractionResult.controls.fields,
            ...result.extractionResult.controls.buttons,
          ];

          const controlButtons = allControls.filter(
            (control) =>
              control.id.replace(/_/g, ' ').toLowerCase().match(forgotPasswordDetectionRegex) ||
              control.label.toLowerCase().match(forgotPasswordDetectionRegex),
          );
          expect(controlButtons.length).toBe(0);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Exclusion Rule: Exclude "Create Account" or "Sign Up"
  it(
    "should exclude 'Create Account', 'Sign Up' or similar action",
    async () => {
      await runTest(
        'Exclude Create Account or Sign up',
        'test/files/screenshots/facebook_login.webp',
        'facebook',
        (result) => {
          const createAccountDetectionRegex =
            /\b(?:(?:create|register)\s+(?:(?:a|an|new)\s+)*(?:account|one)|sign\s+up|register\s+yourself)\b/i;

          const excludedControls =
            result.extractionResult.screenInfo.controlVisibilityRules?.filter((rule) =>
              rule.id.replace(/_/g, ' ').toLowerCase().match(createAccountDetectionRegex),
            );
          excludedControls?.forEach((rule) => {
            expect(rule.status).toBe('excluded');
          });

          const allControls = [
            ...result.extractionResult.controls.fields,
            ...result.extractionResult.controls.buttons,
          ];

          const controlButtons = allControls.filter(
            (control) =>
              control.id.replace(/_/g, ' ').toLowerCase().match(createAccountDetectionRegex) ||
              control.label.toLowerCase().match(createAccountDetectionRegex),
          );
          expect(controlButtons.length).toBe(0);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // 2FA Rule: Extract the verification code from the MFA form
  it(
    'should extract the verification code from the MFA form',
    async () => {
      await runTest(
        'MFA: Extract Verification Code',
        'test/files/screenshots/image-with-auth-code.webp',
        'google',
        (result) => {
          expect(result.classificationResult.screenInfo.screenCode).toBeDefined();
          expect(result.classificationResult.screenInfo.screenCode).toBe('88');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Identify page type as 'authenticated'
  it(
    "should identify page as 'authenticated'",
    async () => {
      await runTest(
        'Page Type: authenticated',
        'test/files/screenshots/github_logged_in.webp',
        'github',
        (result) => {
          expect(result.classificationResult.screenInfo.authState).toBe('authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Identify page type as 'not-authenticated'
  it(
    "should identify page as 'not-authenticated'",
    async () => {
      await runTest(
        'Page Type: not-authenticated',
        'test/files/screenshots/github_login.webp',
        'github',
        (result) => {
          expect(result.classificationResult.screenInfo.authState).toBe('not-authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Identify page type as 'not-authenticated' with captcha
  it(
    "should identify page type as 'not-authenticated' with captcha",
    async () => {
      await runTest(
        'Page Type: not-authenticated with captcha',
        'test/files/screenshots/kazeel_captcha.png',
        'kazeel',
        (result) => {
          expect(result.classificationResult.screenInfo.authState).toBe('not-authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Identify page type as 'not-authenticated' with loading state
  it(
    "should identify page type as 'not-authenticated' with loading",
    async () => {
      await runTest(
        'Page Type: not-authenticated with loading',
        'test/files/screenshots/github-loading.png',
        'github',
        (result) => {
          expect(result.classificationResult.screenInfo.authState).toBe('not-authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Identify page type as 'authenticated' when the screenshot gives a hint that the user is already authenticated
  it(
    "should identify page type as 'authenticated' when the screenshot gives a hint that the user is already authenticated(case trust this device popup)",
    async () => {
      await runTest(
        'Page Type: authenticated',
        'test/files/screenshots/facebook_trust_this_device.png',
        'facebook',
        (result) => {
          expect(result.classificationResult.screenInfo.authState).toBe('authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Half-completed state with title and sudden captcha marked as loading
  it(
    "should identify page type as 'not-authenticated' for half-completed state",
    async () => {
      await runTest(
        'Page Type: not-authenticated with half-completed state',
        'test/files/screenshots/fb-half-complete-state.png',
        'facebook',
        (result) => {
          expect(result.classificationResult.screenInfo.authState).toBe('not-authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Page Type Rule: Loading state detection for various platforms
  it.skip(
    'should identify loading state for various platforms',
    async () => {
      const loadingScreenshots = [
        'test/files/screenshots/github-authenticated-loading.png',
        'test/files/screenshots/kazeel-loading.png',
        'test/files/screenshots/google_loading.png',
        'test/files/screenshots/fb-half-complete-state.png',
        'test/files/screenshots/grayscale/microsoft-blur-loading.webp',
      ];

      for (const screenshot of loadingScreenshots) {
        await runTest(
          `Loading State Detection for ${screenshot.split('/').pop()}`,
          screenshot,
          'google',
          (result) => {
            expect(result.classificationResult.screenInfo.screenClass).toBe('loading-screen');
          },
        );
      }
    },
    TEST_TIMEOUT * 5, // Increased timeout for multiple screenshots
  );

  // Button/Fields Support: Support input text and normal button
  it(
    'should support input text and normal button',
    async () => {
      await runTest(
        'Input Text and Button Support',
        'test/files/screenshots/github_login.webp',
        'github',
        (result) => {
          const formInputs = result.extractionResult.controls.fields.filter(
            (field) => field.actiontype === 'fill',
          );
          expect(formInputs.length).toBe(2);

          const formButtons = result.extractionResult.controls.buttons;
          expect(formButtons.length).toBe(1);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Button/Fields Support: Ensure radio buttons in the same group are presented as a single radio group
  it(
    'should present multiple radio buttons in the same group as a single radio group with options',
    async () => {
      await runTest(
        'Group Radio Buttons',
        'test/files/screenshots/facebook_radios.webp',
        'facebook',
        (result) => {
          const radioFields =
            result.extractionResult.controls?.fields?.filter(
              (field) => field.fieldControlType === 'radiogroup',
            ) || [];

          const radioField = radioFields[0];
          expect(radioFields.length).toBe(1);
          expect(radioField.options?.length).toBe(3);
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Actor Rule: All controls human-first except the ones for remaining authenticated (AI-driven)
  it(
    'should ensure all controls are human-first except the ones for remaining authenticated which are AI-driven',
    async () => {
      await runTest(
        'Actor Rule: Human-first controls except the ones for remaining authenticated',
        'test/files/screenshots/google-acknowledge.webp',
        'google',
        (result) => {
          const rememberMeDetectionRegex =
            /\b(?:remember\s+me|(?:stay|keep\s+me)\s+(?:signed|logged)\s+in|(?:remember|trust)\s+(?:this\s+)?(?:device|computer|browser|pc)|(?:don't|do\s+not)\s+ask\s+again)\b/i;

          const remainAuthenticatedControls = result.extractionResult.controls.fields.filter(
            (control) =>
              control.id.replace(/_/g, ' ').toLowerCase().match(rememberMeDetectionRegex) ||
              control.label.toLowerCase().match(rememberMeDetectionRegex),
          );

          remainAuthenticatedControls.forEach((control) => {
            expect(control.isDontAskAgainControl).toBe(true);
          });
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Passkey: Create a passkey should have an `authenticated` state
  it(
    "should identify page type as 'authenticated' for Create a Passkey screen",
    async () => {
      await runTest(
        'Passkey: Identify page type as authenticated for Create a Passkey screen',
        'test/files/screenshots/google-setup-passkey.png',
        'google',
        (result) => {
          expect(result.classificationResult.screenInfo.authState).toBe('authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  // Passkey: Use a passkey should have an `non-authenticated` state
  it(
    "should identify page type as 'not-authenticated' for Use a Passkey screen",
    async () => {
      await runTest(
        'Passkey: Identify page type as not-authenticated for Use a Passkey screen',
        'test/files/screenshots/passkey.webp',
        'google',
        (result) => {
          expect(result.classificationResult.screenInfo.authState).toBe('not-authenticated');
        },
      );
    },
    TEST_TIMEOUT,
  );

  //State detection llm response tests
  it(
    'should correctly respond to state detection llm calls by comparing the screenshot and formVisualResult in agent',
    async () => {
      const comparisonTests: StateComparisonTester[] = [
        {
          platform: 'facebook',
          imagePath: 'test/files/screenshots/facebook_login.webp',
          visionResultState: facebookLogin,
          expectedResponse: false,
        },
        {
          platform: 'facebook',
          imagePath: 'test/files/screenshots/facebook_radios.webp',
          visionResultState: facebookLogin,
          expectedResponse: true,
        },
        {
          platform: 'facebook',
          imagePath: 'test/files/screenshots/facebook_wrong_credentials.webp',
          visionResultState: facebookLogin,
          expectedResponse: true,
        },
        {
          platform: 'google',
          imagePath: 'test/files/screenshots/google_login.webp',
          visionResultState: googleLogin,
          expectedResponse: false,
        },
        /*{
                    platform: 'google',
                    imagePath: "test/files/screenshots/google_2step_verification.webp",
                    visionResultState: googleLogin,
                    expectedResponse: true
                },
                {
                    platform: 'github',
                    imagePath: "test/files/screenshots/github_login.webp",
                    visionResultState: githubLogin,
                    expectedResponse: false
                },
                {
                    platform: 'github',
                    imagePath: "test/files/screenshots/github_logged_in.webp",
                    visionResultState: githubLogin,
                    expectedResponse: true
                },
                {
                    platform: 'github',
                    imagePath: "test/files/screenshots/github_setup_passkey.webp",
                    visionResultState: githubLogin,
                    expectedResponse: true
                },*/
      ];

      for (const test of comparisonTests) {
        await runStateComparisonTest(
          test.visionResultState,
          test.expectedResponse,
          test.imagePath,
          test.platform,
        );
      }
    },
    TEST_TIMEOUT,
  );
});
